package com.aispeech.hybridspeech

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.hybridspeech.core.MainAppController
import com.aispeech.hybridspeech.core.safeCollect
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap

/**
 * 基于会话（Session）模式的混合语音识别服务。
 * 通过工厂模式创建和管理独立的录音会话。
 */
class HybridSpeechSessionService : Service() {

  companion object {
    private const val TAG = "HybridSpeechSessionSvc" // 使用新标签以区分日志
    private const val NOTIFICATION_ID = 1002 // 使用新的通知ID避免冲突
    private const val CHANNEL_ID = "hybrid_speech_session_channel"
  }

  // 用于管理所有活跃的会话
  private val activeSessions = ConcurrentHashMap<String, RecordingSessionImpl>()

  // 全局配置提供者，由所有会话共享
  private var configProvider: IHybridSpeechConfigProvider? = null

  /**
   * 这是暴露给客户端的 Binder 对象，实现了会话工厂接口。
   */
  private val factoryBinder = object : IHybridSpeechSessionFactory.Stub() {
    override fun createRecordingSession(config: RecordingConfig, callback: IRecordingSessionCallback) {
      val sessionId = UUID.randomUUID().toString()
      AILog.i(TAG, "Request to create new session with ID: $sessionId")

      try {
        // 1. 创建会话的内部实现
        val sessionImpl = RecordingSessionImpl(
          context = applicationContext,
          sessionId = sessionId,
          clientCallback = callback,
          onRelease = { id ->
            AILog.i(TAG, "Session $id released, removing from active list.")
            activeSessions.remove(id)
          }
        )

        // 2. 将会话实例存入Map进行管理
        activeSessions[sessionId] = sessionImpl

        // 3. 创建返回给客户端的 Session Binder 代理
        val sessionStub = createSessionStub(sessionId)

        // 4. 启动会话的内部逻辑（例如，开始录音）
        // 将全局的 configProvider 传递给会话
        sessionImpl.start(config, configProvider)

        // 5. 通过回调，将 IRecordingSession 的代理对象返回给客户端
        callback.onSessionCreated(sessionStub)
        AILog.i(TAG, "Session $sessionId created and returned to client.")

      } catch (e: Exception) {
        AILog.e(TAG, "Failed to create session $sessionId", e)
        activeSessions.remove(sessionId) // 确保清理
        try {
          callback.onSessionCreateFailed("Failed to create session: ${e.message}")
        } catch (re: Exception) {
          AILog.e(TAG, "Failed to notify client about session creation failure", re)
        }
      }
    }

    override fun registerConfigProvider(provider: IHybridSpeechConfigProvider?) {
      AILog.i(TAG, "Global config provider registered.")
      <EMAIL> = provider
    }

    override fun unregisterConfigProvider() {
      AILog.i(TAG, "Global config provider unregistered.")
      <EMAIL> = null
    }
  }

  /**
   * 为指定的 sessionId 创建一个 IRecordingSession.Stub 实例。
   */
  private fun createSessionStub(sessionId: String): IRecordingSession.Stub {
    return object : IRecordingSession.Stub() {
      // 辅助函数，用于安全地获取会话实例
      private fun getSession(): RecordingSessionImpl? {
        val session = activeSessions[sessionId]
        if (session == null) {
          AILog.w(TAG, "Attempted to operate on a released or non-existent session: $sessionId")
        }
        return session
      }

      override fun pause() { getSession()?.pause() }
      override fun resume() { getSession()?.resume() }
      override fun stop() { getSession()?.stop() }
      override fun getStatus(): Int = getSession()?.getStatus() ?: ServiceStatus.IDLE // 返回默认状态
      override fun getRecordingDuration(): Long = getSession()?.getRecordingDuration() ?: 0L
      override fun release() { getSession()?.release() }
    }
  }

  @SuppressLint("InlinedApi")
  override fun onCreate() {
    super.onCreate()
    AILog.i(TAG, "Service onCreate")

    createNotificationChannel()

    val filter = IntentFilter(Intent.ACTION_USER_PRESENT)
    registerReceiver(userPresentReceiver, filter)
    AILog.i(TAG, "USER_PRESENT broadcast receiver registered.")

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
      startForeground(
        NOTIFICATION_ID,
        createNotification(),
        android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE
      )
    } else {
      startForeground(NOTIFICATION_ID, createNotification())
    }
    AILog.i(TAG, "Service started in foreground.")
  }

  override fun onBind(intent: Intent?): IBinder {
    AILog.i(TAG, "Service onBind, returning session factory binder.")
    return factoryBinder
  }

  override fun onUnbind(intent: Intent?): Boolean {
    AILog.i(TAG, "Service onUnbind. Active sessions: ${activeSessions.size}")
    return super.onUnbind(intent)
  }

  override fun onDestroy() {
    AILog.i(TAG, "Service onDestroy")

    // 清理所有仍然活跃的会话
    AILog.i(TAG, "Releasing all active sessions...")
    activeSessions.values.forEach { it.release() }
    activeSessions.clear()

    unregisterReceiver(userPresentReceiver)
    AILog.i(TAG, "USER_PRESENT broadcast receiver unregistered.")

    super.onDestroy()
  }

  // --- 通知相关代码 (与你原有的实现基本一致) ---

  private fun createNotificationChannel() {
    val channel = NotificationChannel(
      CHANNEL_ID,
      "Hybrid Speech Session Service",
      NotificationManager.IMPORTANCE_LOW
    ).apply {
      description = "Manages hybrid speech recognition sessions"
      setShowBadge(false)
    }
    getSystemService(NotificationManager::class.java).createNotificationChannel(channel)
  }

  private fun createNotification(): Notification {
    // 在会话模式下，通知可以更通用，或者反映活跃会话的数量
    val contentText = if (activeSessions.isEmpty()) {
      "待机中"
    } else {
      "正在处理 ${activeSessions.size} 个语音会话"
    }

    return NotificationCompat.Builder(this, CHANNEL_ID)
      .setContentTitle("混合语音服务")
      .setContentText(contentText)
      .setSmallIcon(android.R.drawable.ic_btn_speak_now)
      .setOngoing(true)
      .setShowWhen(false)
      .build()
  }

  private fun updateNotification() {
    val notification = createNotification()
    getSystemService(NotificationManager::class.java).notify(NOTIFICATION_ID, notification)
  }

  // --- 广播接收器 (与你原有的实现一致) ---

  private val userPresentReceiver = object : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
      if (intent?.action == Intent.ACTION_USER_PRESENT) {
        AILog.i(TAG, "User unlocked screen. Ensuring service is running.")
        try {
          // 注意：这里要启动正确的 Service 类
          context?.startForegroundService(Intent(context, HybridSpeechSessionService::class.java))
        } catch (e: Exception) {
          AILog.e(TAG, "Failed to start HybridSpeechSessionService on user present", e)
        }
      }
    }
  }
}


/**
 * 内部会话处理器类。
 * 这是服务端的实现细节，封装了单次录音的完整逻辑。
 * 每个实例都拥有自己的 MainAppController。
 */
private class RecordingSessionImpl(
  private val context: Context,
  private val sessionId: String,
  private val clientCallback: IRecordingSessionCallback,
  private val onRelease: (String) -> Unit // 用于通知 Service 清理会话的回调
) {
  private val TAG = "RecordingSession-$sessionId"

  // 每个会话拥有自己的 MainAppController 和 CoroutineScope
  private val mainController = MainAppController(context)
  private val sessionScopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getMainParentScope()
  )
  private val sessionScope: CoroutineScope = sessionScopeDelegate

  private var eventSubscriptionJob: Job? = null

  init {
    AILog.i(TAG, "Session instance created.")
  }

  fun start(config: RecordingConfig, provider: IHybridSpeechConfigProvider?) {
    AILog.i(TAG, "Starting session...")
    subscribeToEvents() // 在启动时才开始订阅事件

    val startCallback = object : IStartRecordingCallback.Stub() {
      override fun onStartRecordingSuccess() {
        AILog.i(TAG, "Session started successfully.")
        // 启动成功后，可以开始注册进度回调
        mainController.registerProgressCallback(object : IRecordProgressCallback.Stub() {
          override fun onRecordingProgress(durationMs: Long) {
            try {
              clientCallback.onProgressUpdate(durationMs)
            } catch (e: Exception) {
              AILog.e(TAG, "Error sending progress update to client", e)
            }
          }

          override fun onRecordingStarted() {
            // 可以在这里处理录音开始事件，如果需要的话
          }

          override fun onRecordingStopped(totalDurationMs: Long) {
            // 可以在这里处理录音停止事件，如果需要的话
          }

          override fun onError(error: String) {
            try {
              clientCallback.onError(0, "Progress callback error: $error")
            } catch (e: Exception) {
              AILog.e(TAG, "Error sending progress error to client", e)
            }
          }
        }, 100) // 假设回调间隔为100ms
      }

      override fun onStartRecordingError(errorCode: Int, message: String?) {
        AILog.e(TAG, "Session start failed: $message")
        try {
          clientCallback.onError(errorCode, message ?: "Unknown start error")
        } catch (e: Exception) {
          AILog.e(TAG, "Error sending start error to client", e)
        }
        release() // 启动失败，直接释放
      }
    }

    if (provider != null) {
      mainController.startRecordingWithProvider(config, provider, startCallback)
    } else {
      mainController.startRecordingWithConfigAsync(config, startCallback)
    }
  }

  fun pause() = mainController.pauseRecordingAsync(object : IPauseRecordingCallback.Stub() {
    override fun onPauseRecordingSuccess() {
      try { clientCallback.onPaused() } catch (e: Exception) { AILog.e(TAG, "Error in onPaused", e) }
    }
    override fun onPauseRecordingError(errorCode: Int, errorMessage: String?) {
      try { clientCallback.onError(errorCode, "Pause failed: $errorMessage") } catch (e: Exception) { AILog.e(TAG, "Error in onPauseError", e) }
    }
  })

  fun resume() = mainController.resumeRecordingAsync(object : IResumeRecordingCallback.Stub() {
    override fun onResumeRecordingSuccess() {
      try { clientCallback.onResumed() } catch (e: Exception) { AILog.e(TAG, "Error in onResumed", e) }
    }
    override fun onResumeRecordingError(errorCode: Int, errorMessage: String?) {
      try { clientCallback.onError(errorCode, "Resume failed: $errorMessage") } catch (e: Exception) { AILog.e(TAG, "Error in onResumeError", e) }
    }
  })

  fun stop() = mainController.stopRecordingWithResultAsync(object : IStopRecordingCallback.Stub() {
    override fun onStopRecordingSuccess(result: RecordingResultInfo?) {
      try { clientCallback.onStopped(result) } catch (e: Exception) { AILog.e(TAG, "Error in onStopped", e) }
      release() // 停止后自动释放
    }
    override fun onStopRecordingError(errorCode: Int, errorMessage: String?) {
      try { clientCallback.onError(errorCode, "Stop failed: $errorMessage") } catch (e: Exception) { AILog.e(TAG, "Error in onStopRecordingError", e) }
      release() // 出错也释放
    }
  })

  fun getStatus(): Int = mainController.getCurrentStatus()
  fun getRecordingDuration(): Long = mainController.getRecordingDuration()

  fun release() {
    AILog.i(TAG, "Releasing session resources...")
    eventSubscriptionJob?.cancel()
    mainController.release()
    onRelease(sessionId) // 通知 Service 将自己从 Map 中移除
    AILog.i(TAG, "Session released.")
  }

  private fun subscribeToEvents() {
    eventSubscriptionJob = sessionScope.launch {
      // 订阅转写结果
      launch {
        mainController.transcriptionResultFlow.safeCollect(TAG) { result ->
          try {
            clientCallback.onTranscriptionUpdate(result.toRecordingResultInfo())
          } catch (e: Exception) { AILog.e(TAG, "Error notifying transcription result", e) }
        }
      }
      // 订阅错误
      launch {
        mainController.errorFlow.safeCollect(TAG) { error ->
          try {
            clientCallback.onError(0, error) // 适配错误码
          } catch (e: Exception) { AILog.e(TAG, "Error notifying error", e) }
        }
      }
      // 订阅状态变化 (可选，如果需要的话)
      launch {
        mainController.statusChangeFlow.safeCollect(TAG) { status ->
          // 可以在这里做一些内部处理，但通常不需要再通知客户端，因为客户端通过 pause/resume/stop 的回调已经知道状态了
          AILog.d(TAG, "Internal status changed to: $status")
        }
      }
    }
  }

  private fun TranscriptionResult.toRecordingResultInfo(): RecordingResultInfo {
    val text = when (this) {
      is TranscriptionResult.ProcessingTextResult -> this.text
      is TranscriptionResult.FinalTextResult -> this.text ?: ""
      is TranscriptionResult.IntermediateResult -> this.`var`
      is TranscriptionResult.StartResult -> this.message
      is TranscriptionResult.AgendaResult -> this.text ?: ""
      is TranscriptionResult.InitializationResult -> this.results.joinToString(" ") { it.text }
    }

    return RecordingResultInfo(
      pcmFilePath = "", // 转写结果中没有文件路径信息
      mp3FilePath = "",
      durationMs = 0L, // 转写结果中没有时长信息
      fileSizeBytes = 0L,
      audioConfig = AudioRecordingConfig.createDefault() // 使用默认配置
    )
  }
}
